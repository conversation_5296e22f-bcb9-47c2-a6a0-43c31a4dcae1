.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1628rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.group_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_1 {
  width: 402rpx;
  height: 46rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 22rpx 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 12rpx;
}
.text_1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.group_2 {
  width: 750rpx;
  height: 1454rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.box_2 {
  width: 218rpx;
  height: 66rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_2 {
  width: 182rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 36rpx;
}
.thumbnail_4 {
  width: 16rpx;
  height: 32rpx;
  margin-top: 0rpx;
}
.list_1 {
  width: 750rpx;
  height: 990rpx;
  margin-top: 14rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.list-items_1-0 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-0 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-0 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-0 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-0 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-0 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-0 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.list-items_1-1 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-1 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-1 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-1 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-1 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-1 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.list-items_1-2 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-2 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-2 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-2 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-2 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-2 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.list-items_1-3 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-3 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-3 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-3 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-3 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-3 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-3 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.list-items_1-4 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-4 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-4 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-4 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-4 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-4 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-4 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.list-items_1-5 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-5 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-5 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-5 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-5 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-5 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-5 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.list-items_1-6 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-6 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-6 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-6 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-6 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-6 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-6 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.list-items_1-7 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 122rpx;
  margin-bottom: 2rpx;
  flex-direction: row;
  display: flex;
}
.image-text_1-7 {
  width: 342rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 42rpx;
}
.label_1-7 {
  width: 46rpx;
  height: 46rpx;
  margin-top: 24rpx;
}
.text-group_1-7 {
  width: 264rpx;
  height: 90rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_3-7 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_4-7 {
  width: 264rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.text_5-7 {
  width: 98rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 34rpx 30rpx 0 238rpx;
}
.box_3 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 316rpx 0 30rpx 232rpx;
}