<view class="page">
  <view class="block_1">
    <view class="group_1">
      <!-- 全部 -->
      <view class="image-text_1" data-id="all" bindtap="onTabClick">
        <text lines="1" class="text-group_1">{{filterTabs[0].name}}</text>
      </view>
      <!-- 待付款 -->
      <text lines="1" class="text_1" data-id="pending" bindtap="onTabClick">{{filterTabs[1].name}}</text>
      <!-- 已付款 -->
      <view class="box_1" data-id="distribution" bindtap="onTabClick">{{filterTabs[1].name}}</view>
      <!-- 已核销-->
      <view class="box_1" data-id="distribution" bindtap="onTabClick">{{filterTabs[1].name}}</view>
      <!-- 已取消/退款 -->
      <view class="box_1" data-id="distribution" bindtap="onTabClick">{{filterTabs[5].name}}</view>
    </view>
    <!-- 选中样式下蓝条 -->
    <view class="group_2">
      <view class="group_3"></view>
    </view>
  </view>
  <view class="block_2">
    <view class="box_2">
      <view wx:for="{{distributionOrders}}" wx:key="id" class="{{index === 0 ? 'box_3' : (index === 1 ? 'box_4' : (index === 2 ? 'box_5' : 'box_6'))}}" data-id="{{item.id}}" bindtap="onOrderClick">
        <view class="{{index === 0 ? 'text-wrapper_2' : (index === 1 ? 'text-wrapper_4' : (index === 2 ? 'text-wrapper_7' : 'text-wrapper_10'))}}">
          <text lines="1" class="{{index === 0 ? 'text_6' : (index === 1 ? 'text_14' : (index === 2 ? 'text_23' : 'text_32'))}}">订单号:{{item.orderNo}}</text>
          <text lines="1" class="{{index === 0 ? 'text_7' : (index === 1 ? 'text_15' : (index === 2 ? 'text_24' : 'text_33'))}}">{{item.statusText}}</text>
        </view>
        <view class="{{index === 0 ? 'image-text_2' : (index === 1 ? 'image-text_3' : (index === 2 ? 'image-text_4' : 'image-text_5'))}}">
          <image src="{{item.productImage}}" class="{{index === 0 ? 'image_3' : (index === 1 ? 'image_4' : (index === 2 ? 'image_5' : 'image_6'))}}"></image>
          <view class="{{index === 0 ? 'text-group_2' : (index === 1 ? 'text-group_3' : (index === 2 ? 'text-group_4' : 'text-group_5'))}}">
            <text lines="1" class="{{index === 0 ? 'text_8' : (index === 1 ? 'text_16' : (index === 2 ? 'text_25' : 'text_34'))}}">{{item.productName}}</text>
            <text lines="1" decode="true" class="{{index === 0 ? 'text_9' : (index === 1 ? 'text_17' : (index === 2 ? 'text_26' : 'text_35'))}}">下单时间:{{item.orderTime}}</text>
            <view wx:if="{{item.showVerifyTime}}" class="{{index === 0 ? 'text-wrapper_3' : ''}}">
              <text lines="1" decode="true" class="{{index === 0 ? 'text_10' : ''}}">核销时间:{{item.verifyTime}}</text>
            </view>
            <view wx:else class="{{index === 1 ? 'text-wrapper_5' : (index === 2 ? 'text-wrapper_8' : 'text-wrapper_11')}}">
              <text lines="1" class="{{index === 1 ? 'text_18' : (index === 2 ? 'text_27' : 'text_36')}}">核销时间:</text>
              <text lines="1" class="{{index === 1 ? 'text_19' : (index === 2 ? 'text_28' : 'text_37')}}">{{item.verifyTime}}</text>
            </view>
            <view class="{{index === 0 ? 'text-wrapper_3' : (index === 1 ? 'text-wrapper_6' : (index === 2 ? 'text-wrapper_9' : 'text-wrapper_12'))}}">
              <text lines="1" class="{{index === 0 ? 'text_11' : (index === 1 ? 'text_20' : (index === 2 ? 'text_29' : 'text_38'))}}">预计佣金：</text>
              <text lines="1" class="{{index === 0 ? 'text_12' : (index === 1 ? 'text_21' : (index === 2 ? 'text_30' : 'text_39'))}}">￥</text>
              <text lines="1" class="{{index === 0 ? 'text_13' : (index === 1 ? 'text_22' : (index === 2 ? 'text_31' : 'text_40'))}}">{{item.commission}}</text>
            </view>
          </view>
        </view>
      </view>

    </view>
  </view>
</view>