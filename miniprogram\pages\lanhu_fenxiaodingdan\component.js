Page({
  data: {
    // 筛选标签
    filterTabs: [
      { id: 'all', name: '全部', active: true },
      { id: 'pending', name: '待付款', active: false },
      { id: 'distribution', name: '分销订单', active: false },
      { id: 'paid', name: '已付款', active: false },
      { id: 'used', name: '已核销', active: false },
      { id: 'cancelled', name: '已取消/退款', active: false }
    ],

    // 当前选中的筛选类型
    currentFilter: 'all',

    // 原始分销订单列表（用于筛选）
    allDistributionOrders: [
      {
        id: 1,
        orderNo: '0191014156996111236',
        status: 'used',
        statusText: '已使用',
        productName: '苏州博物馆|细品苏式风雅..',
        productImage: '../../images/lanhu_fenxiaodingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png',
        orderTime: '2019-11-04 15:42:01',
        verifyTime: '2019-11-04 15:42:01',
        commission: 256,
        showVerifyTime: true
      },
      {
        id: 2,
        orderNo: '0191014156996111237',
        status: 'paid',
        statusText: '已付款',
        productName: '苏州博物馆|细品苏式风雅..',
        productImage: '../../images/lanhu_fenxiaodingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png',
        orderTime: '2019-11-04 15:42:01',
        verifyTime: '未核销',
        commission: 256,
        showVerifyTime: false
      },
      {
        id: 3,
        orderNo: '0191014156996111238',
        status: 'pending',
        statusText: '待付款',
        productName: '苏州博物馆|细品苏式风雅..',
        productImage: '../../images/lanhu_fenxiaodingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png',
        orderTime: '2019-11-04 15:42:01',
        verifyTime: '未付款',
        commission: 256,
        showVerifyTime: false
      },
      {
        id: 4,
        orderNo: '0191014156996111239',
        status: 'cancelled',
        statusText: '已取消',
        productName: '苏州博物馆|细品苏式风雅..',
        productImage: '../../images/lanhu_fenxiaodingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png',
        orderTime: '2019-11-04 15:42:01',
        verifyTime: '已取消',
        commission: 256,
        showVerifyTime: false
      }
    ],

    // 当前显示的分销订单列表
    distributionOrders: []
  },

  onLoad: function(options) {
    console.log('分销订单页面加载');
    // 初始化显示所有订单
    this.setData({
      distributionOrders: this.data.allDistributionOrders
    });
  },

  // 切换筛选标签
  onTabClick: function(e) {
    const tabId = e.currentTarget.dataset.id;
    const tabs = this.data.filterTabs.map(tab => ({
      ...tab,
      active: tab.id === tabId
    }));

    this.setData({
      filterTabs: tabs,
      currentFilter: tabId
    });

    // 筛选订单
    this.filterOrders(tabId);
  },

  // 筛选订单
  filterOrders: function(tabId) {
    let filteredOrders = [];

    switch(tabId) {
      case 'all':
        filteredOrders = this.data.allDistributionOrders;
        break;
      case 'pending':
        filteredOrders = this.data.allDistributionOrders.filter(order => order.status === 'pending');
        break;
      case 'paid':
        filteredOrders = this.data.allDistributionOrders.filter(order => order.status === 'paid');
        break;
      case 'used':
        filteredOrders = this.data.allDistributionOrders.filter(order => order.status === 'used');
        break;
      case 'cancelled':
        filteredOrders = this.data.allDistributionOrders.filter(order => order.status === 'cancelled');
        break;
      case 'distribution':
        // 分销订单可以显示已付款和已核销的订单
        filteredOrders = this.data.allDistributionOrders.filter(order =>
          order.status === 'paid' || order.status === 'used'
        );
        break;
      default:
        filteredOrders = this.data.allDistributionOrders;
    }

    this.setData({
      distributionOrders: filteredOrders
    });

    console.log('筛选订单:', tabId, '结果数量:', filteredOrders.length);
  },

  // 点击订单项
  onOrderClick: function(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('点击订单:', orderId);
    // 这里可以跳转到订单详情页面
    // wx.navigateTo({
    //   url: `/pages/order-detail/index?id=${orderId}`
    // });
  }
})
